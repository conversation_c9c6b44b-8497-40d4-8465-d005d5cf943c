'''
Author: kangrl
Email: <EMAIL>
Date: 2025-06-08 21:05:47
LastEditors: kangrl
LastEditTime: 2025-06-08 21:31:56
FilePath: /Design Patterns & Deep Learning/Design Patterns/Observer/WaterHeater.py
Copyright (C) 2025 by kangrl, All Rights Reserved.
Description:
'''

"""
故事梗概：程序监听一个热水器：当热水器温度达到100度，提示水烧开了，可以喝水了；当热水器温度低于70度、高于50度，提示水烧好了，可以洗澡了。
"""

from abc import ABCMeta, abstractmethod


class Observer(metaclass=ABCMeta):
    """ 观察者 """

    @abstractmethod
    def update(self, observable):
        pass


class Observable(metaclass=ABCMeta):
    """ 被观察者 """

    def __init__(self):
        self.__observers = []

    def addObserver(self, observer):
        self.__observers.append(observer)

    def removeObserver(self, observer):
        self.__observers.remove(observer)

    def notifyObservers(self):
        for observer in self.__observers:
            observer.update(self)


class WaterHeater(Observable):
    """ 热水器 """

    def __init__(self):
        super().__init__()
        self.__temperature = 25

    def getTemperature(self):
        return self.__temperature

    def setTemperature(self, temperature):
        self.__temperature = temperature
        print("Current temperature is: ", temperature)
        self.notifyObservers()


class WashingMode(Observer):
    """ 洗澡模式 """

    def update(self, observable):
        if isinstance(observable, WaterHeater) and observable.getTemperature() < 70 and observable.getTemperature() > 50:
            print("The water is just right for washing.")


class DrinkingMode(Observer):
    """ 喝水模式 """

    def update(self, observable):
        if isinstance(observable, WaterHeater) and observable.getTemperature() == 100:
            print("The water is boiled, you can drink it.")


if __name__ == "__main__":
    heater = WaterHeater()
    washingOb = WashingMode()
    drinkingOb = DrinkingMode()

    heater.addObserver(washingOb)
    heater.addObserver(drinkingOb)

    heater.setTemperature(40)
    heater.setTemperature(60)
    heater.setTemperature(100)
