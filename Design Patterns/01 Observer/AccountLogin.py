'''
Author: kangrl
Email: <EMAIL>
Date: 2025-06-08 21:41:13
LastEditors: kangrl
LastEditTime: 2025-06-21 09:10:00
FilePath: /Design Patterns & Deep Learning/Design Patterns/01 Observer/AccountLogin.py
Copyright (C) 2025 by kangrl, All Rights Reserved.
Description:
'''

"""
故事梗概：程序监听一个账号登录：如果本次登录和上次登录的IP地址不同，则提示异地登录，异地登陆后发送登录异常信息至短信和邮件。
"""

from abc import ABCMeta, abstractmethod
from datetime import datetime


class Observer(metaclass=ABCMeta):
    """ 观察者 """

    @abstractmethod
    def update(self, observable, object):
        pass


class Observable(metaclass=ABCMeta):
    """ 被观察者 """

    def __init__(self):
        self.__observers = []

    def addObserver(self, observer):
        self.__observers.append(observer)

    def removeObserver(self, observer):
        self.__observers.remove(observer)

    def notifyObservers(self, object):
        for observer in self.__observers:
            observer.update(self, object )


class Account(Observable):
    """ 账号 """

    def __init__(self):
        super().__init__()
        self.__latest_ip = {}
        self.__latest_region = {}

    def login(self, name, ip, time):
        """ 登录 """

        region = self.__get_region(ip)
        if self.__is_long_distance(name, region):
            self.notifyObservers({"name": name, "ip": ip, "region": region, "time": time})

        self.__latest_region[name] = region
        self.__latest_ip[name] = ip

    def __get_region(self, ip):
        """ 判断是否异地登录 """
        ip_regions = {
            "***********": "China",
            "**************": "America",
        }

        region = ip_regions.get(ip)

        return "" if region is None else region

    def __is_long_distance(self, name, region):
        """ 获取IP地址所在地区 """

        latest_region = self.__latest_region.get(name)

        return latest_region is not None and latest_region != region


class SmsSender(Observer):
    """ 短信发送者 """

    def update(self, observable, object):
        print(f"Detect long distance login:")
        print(f"Send sms to {object['name']} from {object['ip']} in {object['region']} at {object['time']}")


class EmailSender(Observer):
    """ 邮件发送者 """

    def update(self, observable, object):
        print(f"Detect long distance login:")
        print(f"Send email to {object['name']} from {object['ip']} in {object['region']} at {object['time']}")


if __name__ == "__main__":
    acc = Account()
    sms_sender = SmsSender()
    email_sender = EmailSender()

    acc.addObserver(sms_sender)
    acc.addObserver(email_sender)

    acc.login("admin", "***********", datetime.now())
    acc.login("admin", "**************", datetime.now())
