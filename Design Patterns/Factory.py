'''
Author: kangrl
Email: <EMAIL>
Date: 2025-06-08 19:27:29
LastEditors: kangrl
LastEditTime: 2025-06-08 19:50:50
FilePath: /Design Patterns & Deep Learning/Design Paterns/Factory/NameFactory.py
NameFactory.py
Copyright (C) 2025 by kangrl, All Rights Reserved.
Description:
'''


class Namer:
    """ Name Class """

    def __init__(self):
        self.last_name = ""
        self.first_name = ""


class LastFirstNamer(Namer):
    """ Last First Name Class """

    def __init__(self, name_string):
        super().__init__()
        self._parse_name(name_string)

    def _parse_name(self, name_string):
        """解析 姓,名 格式的姓名"""
        if "," in name_string:
            name_parts = name_string.split(",")
            self.last_name = name_parts[0].strip()
            self.first_name = name_parts[1].strip()
        else:
            # if no comma, the whole string is the last name
            self.last_name = name_string


class FirstLastNamer(Namer):
    """ First Last Name Class """

    def __init__(self, name_string):
        super().__init__()
        self._parse_name(name_string)

    def _parse_name(self, name_string):
        """解析 名 姓 格式的姓名"""
        if " " in name_string:
            name_parts = name_string.split(" ")
            self.first_name = name_parts[0].strip()
            self.last_name = name_parts[1].strip()
        else:
            # if no space, the whole string is the first name
            self.first_name = name_string


class NameFactory:
    """ Name Factory Class """

    def __init__(self, name_string):
        self.name_string = name_string

    def get_namer(self):
        if " " in self.name_string:
            return FirstLastNamer(self.name_string)
        else:
            return LastFirstNamer(self.name_string)


class Builder:
    """ Builder Class """

    def __init__(self):
        self.name_string = ""

    def get_namer(self):
        while True:
            name_string = input("Enter a name: ")
            if name_string == "" or name_string == "quit":
                break

            name_factory = NameFactory(name_string)
            namer = name_factory.get_namer()

            print(namer.last_name, namer.first_name)


if __name__ == "__main__":
    builder = Builder()
    builder.get_namer()
