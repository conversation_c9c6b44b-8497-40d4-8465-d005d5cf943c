'''
Author: kangrl
Email: <EMAIL>
Date: 2025-06-22 09:46:22
LastEditors: kangrl
LastEditTime: 2025-06-22 16:22:07
FilePath: /Design Patterns & Deep Learning/Deep Learning/CLIP/src/model.py
Copyright (C) 2025 by kangrl, All Rights Reserved.
Description:
'''

import torch
import torch.nn as nn
import torch.nn.functional as F

from torchvision import models
from transformers import AutoModel
from transformers import BertTokenizer
from transformers import AutoTokenizer

from .config import Config
from .loss import clip_loss, metrics


class Projection(nn.Module):
    """将图像或文本特征投影到同一嵌入特征空间

    Args:
        nn (_type_): _description_
    """

    def __init__(self, input_dim: int, output_dim: int) -> None:
        super(Projection, self).__init__()
        self.linear1 = nn.Linear(input_dim, output_dim, bias=False)
        self.linear2 = nn.Linear(output_dim, output_dim, bias=False)
        self.layer_norm = nn.LayerNorm(output_dim)
        self.dropout = nn.Dropout(Config.dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        embed1 = self.linear1(x)
        embed2 = self.dropout(self.linear2(F.gelu(embed1)))
        embeds = self.layer_norm(embed1 + embed2)
        return embeds


class VisionEncoder(nn.Module):
    """视觉编码器

    Args:
        nn (_type_): _description_
    """

    def __init__(self, output_dim: int) -> None:
        super(VisionEncoder, self).__init__()
        self.model = models.resnet50(weights=models.ResNet50_Weights.IMAGENET1K_V2)
        self.projection = Projection(self.model.fc.in_features, output_dim)
        self.model.fc = nn.Identity()

        for param in self.model.parameters():
            param.requires_grad = False

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        projection_embeds = self.projection(self.model(x))
        return projection_embeds / projection_embeds.norm(dim=-1, keepdim=True)


class TextEncoder(nn.Module):
    """文本编码器

    Args:
        nn (_type_): _description_
    """

    def __init__(self, output_dim: int) -> None:
        super(TextEncoder, self).__init__()
        self.model = AutoModel.from_pretrained(Config.text_model_type)
        self.projection = Projection(Config.transformer_embed_dim, output_dim)

        for param in self.model.parameters():
            param.requires_grad = False

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        projection_embeds = self.projection(self.model(x)[0][:, 0, :])  # [batch_size, embed_dim] 取[CLS]的输出
        return projection_embeds / projection_embeds.norm(dim=-1, keepdim=True)


class Tokenizer(nn.Module):
    """文本编码器

    Args:
        nn (_type_): _description_
    """

    def __init__(self, tokenizer: BertTokenizer) -> None:
        super(Tokenizer, self).__init__()
        self.tokenizer = tokenizer

    def __call__(self, text: str) -> torch.Tensor:
        return self.tokenizer(
                text,
                max_length=Config.max_length,
                padding=True,
                truncation=True,
                return_tensors="pt"
            )


class CLIP(nn.Module):
    """CLIP模型

    Args:
        nn (_type_): _description_
    """

    def __init__(self) -> None:
        super(CLIP, self).__init__()

        self.vision_encoder = VisionEncoder(Config.embed_dim)
        self.caption_encoder = TextEncoder(Config.embed_dim)

        self.tokenizer = Tokenizer(
            BertTokenizer.from_pretrained(Config.text_model_type)
        )

        self.device = Config.device
        self.learning_rate = Config.learning_rate

    def forward(self, image: torch.Tensor, text: torch.Tensor) -> torch.Tensor:
        text_tokens = self.tokenizer(text).to(self.device)

        image_embeds = self.vision_encoder(image)
        caption_embeds = self.caption_encoder(text_tokens["input_ids"])

        similarity = torch.matmul(caption_embeds, image_embeds.T)

        loss = clip_loss(similarity)
        img2cap_match_acc, cap2img_match_acc = metrics(similarity)

        return loss, img2cap_match_acc, cap2img_match_acc
