'''
Author: kangrl
Email: <EMAIL>
Date: 2025-06-22 09:52:56
LastEditors: kangrl
LastEditTime: 2025-06-22 13:05:38
FilePath: /Design Patterns & Deep Learning/Deep Learning/CLIP/src/config.py
Copyright (C) 2025 by kangrl, All Rights Reserved.
Description:
'''

import torch
from dataclasses import dataclass

@dataclass
class Config:
    """CLIP配置类

    Args:
        dataclass (_type_): 数据类
    """

    embed_dim: int = 512  # 嵌入维度
    transformer_embed_dim: int = 768
    image_resolution: int = 224  # 图像分辨率
    max_length: int = 64  # 文本最大长度
    vision_model_type: str = "resnet50"  # 视觉模型类型
    text_model_type: str = "distilbert-base-multilingual-cased"  # 文本模型类型
    tokenizer: str = "bert-base-uncased"  # 文本编码器类型
    dropout: float = 0.5  # 丢弃率
    epochs: int = 10  # 训练轮数
    learning_rate: float = 1e-3  # 学习率
    batch_size: int = 256  # 批量大小
    device: str = "mps" if torch.backends.mps.is_available() else "cpu"  # 设备
