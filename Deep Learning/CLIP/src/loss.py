'''
Author: kangrl
Email: <EMAIL>
Date: 2025-06-22 09:53:17
LastEditors: kangrl
LastEditTime: 2025-06-22 16:33:52
FilePath: /Design Patterns & Deep Learning/Deep Learning/CLIP/src/loss.py
Copyright (C) 2025 by kangrl, All Rights Reserved.
Description:
'''

import torch
import torch.nn.functional as F


def clip_loss(logits: torch.Tensor) -> torch.Tensor:
    """Calculate a cross entropy loss function for CLIP

    Args:
        logits (torch.Tensor): _description_

    Returns:
        torch.Tensor: _description_
    """

    n = logits.shape[1]

    # Create labels
    labels = torch.arange(n, device=logits.device)

    # Calculate loss
    loss_i = F.cross_entropy(logits, labels, reduction="mean", axis=0)
    loss_t = F.cross_entropy(logits, labels, reduction="mean", axis=1)

    return (loss_i + loss_t) / 2


def metrics(similarity: torch.Tensor) -> torch.Tensor:
    """Calculate the metrics for CLIP

    Args:
        similarity (torch.Tensor): _description_

    Returns:
        torch.Tensor: _description_
    """

    y = torch.arange(similarity.shape[0], device=similarity.device)

    img2cap_match_idx = similarity.argmax(dim=1)
    cap2img_match_idx = similarity.argmax(dim=0)

    img2cap_match_acc = (img2cap_match_idx == y).float().mean()
    cap2img_match_acc = (cap2img_match_idx == y).float().mean()

    return img2cap_match_acc, cap2img_match_acc
