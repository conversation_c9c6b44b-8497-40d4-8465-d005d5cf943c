'''
Author: kangrl
Email: <EMAIL>
Date: 2025-06-22 11:06:05
LastEditors: kangrl
LastEditTime: 2025-06-22 12:29:02
FilePath: /Design Patterns & Deep Learning/Deep Learning/CLIP/src/hubda.py
Copyright (C) 2025 by kangrl, All Rights Reserved.
Description:
'''

import os
import json
import collections

import torch
import torchvision.transforms as transforms

from PIL import Image
from datasets import load_dataset
from torch.utils.data import Dataset


class Flickr30kDataset(Dataset):
    """Flickr30k数据集
    """

    def __init__(self):
        self.dataset = load_dataset("nlphuji/flickr30k", cache_dir="../../Data/flickr30k")
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            # transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        self.cap_per_image = 5

    def __len__(self):
        return self.dataset.num_rows['test'] * self.cap_per_image

    def __getitem__(self, idx):
        original_idx = idx // self.cap_per_image
        # image_path = self.dataset[idx]["image_path"]
        image = self.dataset["test"][original_idx]["image"].convert("RGB")
        image = self.transform(image)

        # You might need to adjust the labels based on your task
        caption = self.dataset["test"][original_idx]["caption"][
            idx % self.cap_per_image
        ]

        return {"image": image, "caption": caption}
