{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CLIP"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from src.hubda import Flickr30kDataset"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "DatasetNotFoundError", "evalue": "Dataset 'flickr30k' doesn't exist on the Hub or cannot be accessed.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mDatasetNotFoundError\u001b[39m                      <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Create an instance of the custom dataset\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m flickr30k_custom_dataset = \u001b[43mFlickr30kDataset\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Local/WorkSpace/GitHub/Design Patterns & Deep Learning/Deep Learning/CLIP/src/hubda.py:29\u001b[39m, in \u001b[36mFlickr30kDataset.__init__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     28\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m---> \u001b[39m\u001b[32m29\u001b[39m     \u001b[38;5;28mself\u001b[39m.dataset = \u001b[43mload_dataset\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mflickr30k\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m../../Data/flickr30k\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     30\u001b[39m     \u001b[38;5;28mself\u001b[39m.transform = transforms.Compose([\n\u001b[32m     31\u001b[39m         transforms.Resize((\u001b[32m224\u001b[39m, \u001b[32m224\u001b[39m)),\n\u001b[32m     32\u001b[39m         transforms.ToTensor(),\n\u001b[32m     33\u001b[39m         \u001b[38;5;66;03m# transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])\u001b[39;00m\n\u001b[32m     34\u001b[39m     ])\n\u001b[32m     36\u001b[39m     \u001b[38;5;28mself\u001b[39m.cap_per_image = \u001b[32m5\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Caskroom/miniconda/base/envs/dl/lib/python3.12/site-packages/datasets/load.py:2062\u001b[39m, in \u001b[36mload_dataset\u001b[39m\u001b[34m(path, name, data_dir, data_files, split, cache_dir, features, download_config, download_mode, verification_mode, keep_in_memory, save_infos, revision, token, streaming, num_proc, storage_options, trust_remote_code, **config_kwargs)\u001b[39m\n\u001b[32m   2057\u001b[39m verification_mode = VerificationMode(\n\u001b[32m   2058\u001b[39m     (verification_mode \u001b[38;5;129;01mor\u001b[39;00m VerificationMode.BASIC_CHECKS) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m save_infos \u001b[38;5;28;01melse\u001b[39;00m VerificationMode.ALL_CHECKS\n\u001b[32m   2059\u001b[39m )\n\u001b[32m   2061\u001b[39m \u001b[38;5;66;03m# Create a dataset builder\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m2062\u001b[39m builder_instance = \u001b[43mload_dataset_builder\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2063\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2064\u001b[39m \u001b[43m    \u001b[49m\u001b[43mname\u001b[49m\u001b[43m=\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2065\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2066\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata_files\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_files\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2067\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2068\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfeatures\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfeatures\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2069\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdownload_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdownload_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2070\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdownload_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdownload_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2071\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2072\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2073\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2074\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2075\u001b[39m \u001b[43m    \u001b[49m\u001b[43m_require_default_config_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mname\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m   2076\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mconfig_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2077\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2079\u001b[39m \u001b[38;5;66;03m# Return iterable dataset in case of streaming\u001b[39;00m\n\u001b[32m   2080\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m streaming:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Caskroom/miniconda/base/envs/dl/lib/python3.12/site-packages/datasets/load.py:1782\u001b[39m, in \u001b[36mload_dataset_builder\u001b[39m\u001b[34m(path, name, data_dir, data_files, cache_dir, features, download_config, download_mode, revision, token, storage_options, trust_remote_code, _require_default_config_name, **config_kwargs)\u001b[39m\n\u001b[32m   1780\u001b[39m     download_config = download_config.copy() \u001b[38;5;28;01mif\u001b[39;00m download_config \u001b[38;5;28;01melse\u001b[39;00m DownloadConfig()\n\u001b[32m   1781\u001b[39m     download_config.storage_options.update(storage_options)\n\u001b[32m-> \u001b[39m\u001b[32m1782\u001b[39m dataset_module = \u001b[43mdataset_module_factory\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1783\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1784\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1785\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdownload_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdownload_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1786\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdownload_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdownload_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1787\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1788\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata_files\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_files\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1789\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1790\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1791\u001b[39m \u001b[43m    \u001b[49m\u001b[43m_require_default_config_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43m_require_default_config_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1792\u001b[39m \u001b[43m    \u001b[49m\u001b[43m_require_custom_configs\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mbool\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mconfig_kwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1793\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1794\u001b[39m \u001b[38;5;66;03m# Get dataset builder class from the processing script\u001b[39;00m\n\u001b[32m   1795\u001b[39m builder_kwargs = dataset_module.builder_kwargs\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Caskroom/miniconda/base/envs/dl/lib/python3.12/site-packages/datasets/load.py:1652\u001b[39m, in \u001b[36mdataset_module_factory\u001b[39m\u001b[34m(path, revision, download_config, download_mode, dynamic_modules_path, data_dir, data_files, cache_dir, trust_remote_code, _require_default_config_name, _require_custom_configs, **download_kwargs)\u001b[39m\n\u001b[32m   1650\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCouldn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt reach the Hugging Face Hub for dataset \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me1\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m   1651\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e1, (DataFilesNotFoundError, DatasetNotFoundError, EmptyDatasetError)):\n\u001b[32m-> \u001b[39m\u001b[32m1652\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e1 \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1653\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e1, \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m):\n\u001b[32m   1654\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m trust_remote_code:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Caskroom/miniconda/base/envs/dl/lib/python3.12/site-packages/datasets/load.py:1578\u001b[39m, in \u001b[36mdataset_module_factory\u001b[39m\u001b[34m(path, revision, download_config, download_mode, dynamic_modules_path, data_dir, data_files, cache_dir, trust_remote_code, _require_default_config_name, _require_custom_configs, **download_kwargs)\u001b[39m\n\u001b[32m   1574\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m DatasetNotFoundError(\n\u001b[32m   1575\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mRevision \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrevision\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m doesn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt exist for dataset \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m on the Hub.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1576\u001b[39m     ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m   1577\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m RepositoryNotFoundError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m-> \u001b[39m\u001b[32m1578\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m DatasetNotFoundError(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mDataset \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m doesn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt exist on the Hub or cannot be accessed.\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m   1579\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m   1580\u001b[39m     dataset_script_path = api.hf_hub_download(\n\u001b[32m   1581\u001b[39m         repo_id=path,\n\u001b[32m   1582\u001b[39m         filename=filename,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1585\u001b[39m         proxies=download_config.proxies,\n\u001b[32m   1586\u001b[39m     )\n", "\u001b[31mDatasetNotFoundError\u001b[39m: Dataset 'flickr30k' doesn't exist on the Hub or cannot be accessed."]}], "source": ["# Create an instance of the custom dataset\n", "flickr30k_custom_dataset = Flickr30kDataset()"]}], "metadata": {"kernelspec": {"display_name": "dl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}