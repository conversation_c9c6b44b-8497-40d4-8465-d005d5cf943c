'''
Author: kangrl
Email: <EMAIL>
Date: 2025-06-22 11:33:03
LastEditors: kangrl
LastEditTime: 2025-06-22 12:53:34
FilePath: /Design Patterns & Deep Learning/Deep Learning/CLIP/train.py
Copyright (C) 2025 by kangrl, All Rights Reserved.
Description:
'''

import os
import sys

# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from torch.utils.data import DataLoader

from src.model import CLIP
from src.config import Config
from src.hubda import Flickr30kDataset


def main():
    print("Loading dataset...")
    clip_dataset = Flickr30kDataset()
    clip_dataloader = DataLoader(clip_dataset, batch_size=Config.batch_size, shuffle=True, num_workers=4)

    print("Loading model...")
    device = Config.device
    model = CLIP().to(device)

    optimizer = torch.optim.Adam(
        [
            {"params": model.vision_encoder.parameters()},
            {"params": model.caption_encoder.parameters()},
        ],
        lr=model.learning_rate,
    )

    print("Training...")
    batch_zero = True
    for epoch in range(Config.epochs):
        model.train()

        for idx, batch in enumerate(clip_dataloader):
            image = batch["image"].to(device)
            caption = batch["caption"]

            loss, img2cap_match_acc, cap2img_match_acc = model(image, caption)

            # Backward
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            if batch_zero:
                print(f"Epoch {epoch} | Batch {idx} / {len(clip_dataloader)} | Loss: {loss.item():.4f} | Image2Caption Accuracy: {img2cap_match_acc:.4f} | Caption2Image Accuracy: {cap2img_match_acc:.4f}")

        # Print the loss and accuracy
        print(f"Epoch {epoch} / {Config.epochs} | Loss: {loss.item():.4f} | Image2Caption Accuracy: {img2cap_match_acc:.4f} | Caption2Image Accuracy: {cap2img_match_acc:.4f}")

    print("Training completed")


if __name__ == "__main__":
    main()
