<!--
 * @Author: kangrl
 * @Email: <EMAIL>
 * @Date: 2025-06-22 16:56:59
 * @LastEditors: kangrl
 * @LastEditTime: 2025-06-22 16:57:00
 * @FilePath: /Design Patterns & Deep Learning/graph.html
 * Copyright (C) 2025 by kangrl, All Rights Reserved.
 * @Description:
-->

<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 1702.1875 425.25" style="max-width: 1702.1875px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-1750582547456-mckb38lqq"><style>#mermaid-svg-1750582547456-mckb38lqq{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#cccccc;}#mermaid-svg-1750582547456-mckb38lqq .error-icon{fill:#5a1d1d;}#mermaid-svg-1750582547456-mckb38lqq .error-text{fill:#f48771;stroke:#f48771;}#mermaid-svg-1750582547456-mckb38lqq .edge-thickness-normal{stroke-width:2px;}#mermaid-svg-1750582547456-mckb38lqq .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-1750582547456-mckb38lqq .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-1750582547456-mckb38lqq .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-1750582547456-mckb38lqq .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-1750582547456-mckb38lqq .marker{fill:#cccccc;stroke:#cccccc;}#mermaid-svg-1750582547456-mckb38lqq .marker.cross{stroke:#cccccc;}#mermaid-svg-1750582547456-mckb38lqq svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-1750582547456-mckb38lqq .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#cccccc;}#mermaid-svg-1750582547456-mckb38lqq .cluster-label text{fill:#e7e7e7;}#mermaid-svg-1750582547456-mckb38lqq .cluster-label span,#mermaid-svg-1750582547456-mckb38lqq p{color:#e7e7e7;}#mermaid-svg-1750582547456-mckb38lqq .label text,#mermaid-svg-1750582547456-mckb38lqq span,#mermaid-svg-1750582547456-mckb38lqq p{fill:#cccccc;color:#cccccc;}#mermaid-svg-1750582547456-mckb38lqq .node rect,#mermaid-svg-1750582547456-mckb38lqq .node circle,#mermaid-svg-1750582547456-mckb38lqq .node ellipse,#mermaid-svg-1750582547456-mckb38lqq .node polygon,#mermaid-svg-1750582547456-mckb38lqq .node path{fill:#23272e;stroke:#404754;stroke-width:1px;}#mermaid-svg-1750582547456-mckb38lqq .flowchart-label text{text-anchor:middle;}#mermaid-svg-1750582547456-mckb38lqq .node .label{text-align:center;}#mermaid-svg-1750582547456-mckb38lqq .node.clickable{cursor:pointer;}#mermaid-svg-1750582547456-mckb38lqq .arrowheadPath{fill:#dcd8d1;}#mermaid-svg-1750582547456-mckb38lqq .edgePath .path{stroke:#cccccc;stroke-width:2.0px;}#mermaid-svg-1750582547456-mckb38lqq .flowchart-link{stroke:#cccccc;fill:none;}#mermaid-svg-1750582547456-mckb38lqq .edgeLabel{background-color:#23272e99;text-align:center;}#mermaid-svg-1750582547456-mckb38lqq .edgeLabel rect{opacity:0.5;background-color:#23272e99;fill:#23272e99;}#mermaid-svg-1750582547456-mckb38lqq .labelBkg{background-color:rgba(35, 39, 46, 0.5);}#mermaid-svg-1750582547456-mckb38lqq .cluster rect{fill:rgba(103, 118, 150, 0.19);stroke:#3e4452;stroke-width:1px;}#mermaid-svg-1750582547456-mckb38lqq .cluster text{fill:#e7e7e7;}#mermaid-svg-1750582547456-mckb38lqq .cluster span,#mermaid-svg-1750582547456-mckb38lqq p{color:#e7e7e7;}#mermaid-svg-1750582547456-mckb38lqq div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#23272e;border:1px solid #3e4452;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-1750582547456-mckb38lqq .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#cccccc;}#mermaid-svg-1750582547456-mckb38lqq :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1750582547456-mckb38lqq_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1750582547456-mckb38lqq_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1750582547456-mckb38lqq_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1750582547456-mckb38lqq_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1750582547456-mckb38lqq_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ViT LE-MAE" id="L-ViT-MAE-0" d="M109.586,125.25L126.726,107.167C143.865,89.083,178.143,52.917,210.316,34.833C242.489,16.75,272.555,16.75,287.589,16.75L302.622,16.75"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ViT LE-CLIP" id="L-ViT-CLIP-0" d="M141.337,125.25L153.185,121.083C165.032,116.917,188.727,108.583,203.858,104.417C218.989,100.25,225.555,100.25,228.839,100.25L232.122,100.25"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ViT LE-ALBEF" id="L-ViT-ALBEF-0" d="M141.337,158.75L153.185,162.917C165.032,167.083,188.727,175.417,228.041,179.583C267.354,183.75,322.287,183.75,349.753,183.75L377.22,183.75"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ViT LE-BEiT" id="L-ViT-BEiT-0" d="M103.236,158.75L121.434,190.75C139.631,222.75,176.027,286.75,227.454,318.75C278.882,350.75,345.341,350.75,411.801,350.75C478.26,350.75,544.72,350.75,581.233,350.75C617.746,350.75,624.313,350.75,627.596,350.75L630.88,350.75"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MAE LE-FL2P" id="L-MAE-FL2P-0" d="M515.68,16.75L531.596,16.75C547.513,16.75,579.346,16.75,637.086,16.75C694.826,16.75,778.472,16.75,820.295,16.75L862.118,16.75"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ALBEF LE-CoCa" id="L-ALBEF-CoCa-0" d="M441.082,177.618L469.432,171.682C497.781,165.746,554.48,153.873,610.02,147.936C665.56,142,719.941,142,747.131,142L774.321,142"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ALBEF LE-VLMO" id="L-ALBEF-VLMO-0" d="M441.082,189.882L469.432,195.818C497.781,201.754,554.48,213.627,609.269,219.564C664.058,225.5,716.935,225.5,743.374,225.5L769.813,225.5"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-VLMO LE-BLIP" id="L-VLMO-BLIP-0" d="M1008.535,225.5L1035.857,225.5C1063.18,225.5,1117.824,225.5,1148.43,225.5C1179.035,225.5,1185.602,225.5,1188.885,225.5L1192.169,225.5"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-BEiT LE-BEiTV2" id="L-BEiT-BEiTV2-0" d="M1004.418,334L1032.427,329.833C1060.435,325.667,1116.452,317.333,1173.098,313.167C1229.744,309,1287.019,309,1315.656,309L1344.294,309"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-BEiT LE-VL-BEiT" id="L-BEiT-VL-BEiT-0" d="M1004.418,367.5L1032.427,371.667C1060.435,375.833,1116.452,384.167,1158.906,388.333C1201.361,392.5,1230.253,392.5,1244.699,392.5L1259.145,392.5"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-BLIP LE-BEiTV3" id="L-BLIP-BEiTV3-0" d="M1568.953,225.5L1573.12,225.5C1577.286,225.5,1585.62,225.5,1597.089,235.902C1608.558,246.304,1623.162,267.108,1630.464,277.51L1637.767,287.912"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-VL-BEiT LE-BEiTV3" id="L-VL-BEiT-BEiTV3-0" d="M1501.977,392.5L1517.306,392.5C1532.635,392.5,1563.294,392.5,1585.926,382.098C1608.558,371.696,1623.162,350.892,1630.464,340.49L1637.767,330.088"/><path marker-end="url(#mermaid-svg-1750582547456-mckb38lqq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-BEiTV2 LE-BEiTV3" id="L-BEiTV2-BEiTV3-0" d="M1416.828,309L1446.349,309C1475.87,309,1534.911,309,1567.716,309C1600.52,309,1607.086,309,1610.37,309L1613.653,309"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(93.7109375, 142)" id="flowchart-ViT-162" class="node default default flowchart-label"><rect height="33.5" width="187.421875" y="-16.75" x="-93.7109375" ry="0" rx="0" style="fill:#f9f;stroke:#333;stroke-width:2px;" class="basic label-container"/><g transform="translate(-86.2109375, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="172.421875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Vision Transformer (ViT)</span></div></foreignObject></g></g><g transform="translate(411.80078125, 16.75)" id="flowchart-MAE-163" class="node default default flowchart-label"><rect height="33.5" width="207.7578125" y="-16.75" x="-103.87890625" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-96.37890625, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="192.7578125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Masked Autoencoder (MAE)</span></div></foreignObject></g></g><g transform="translate(411.80078125, 100.25)" id="flowchart-CLIP-165" class="node default default flowchart-label"><rect height="33.5" width="348.7578125" y="-16.75" x="-174.37890625" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-166.87890625, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="333.7578125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Contrastive Language-Image Pretraining (CLIP)</span></div></foreignObject></g></g><g transform="translate(411.80078125, 183.75)" id="flowchart-ALBEF-167" class="node default default flowchart-label"><rect height="33.5" width="58.5625" y="-16.75" x="-29.28125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-21.78125, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="43.5625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">ALBEF</span></div></foreignObject></g></g><g transform="translate(891.82421875, 350.75)" id="flowchart-BEiT-169" class="node default default flowchart-label"><rect height="33.5" width="511.2890625" y="-16.75" x="-255.64453125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-248.14453125, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="496.2890625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Bidirectional Encoder representation from Image Transformers (BEiT)</span></div></foreignObject></g></g><g transform="translate(891.82421875, 16.75)" id="flowchart-FL2P-171" class="node default default flowchart-label"><rect height="33.5" width="48.8125" y="-16.75" x="-24.40625" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-16.90625, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="33.8125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">FL2P</span></div></foreignObject></g></g><g transform="translate(891.82421875, 142)" id="flowchart-CoCa-173" class="node default default flowchart-label"><rect height="33.5" width="224.40625" y="-16.75" x="-112.203125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-104.703125, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="209.40625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Contrastive Captioner (CoCa)</span></div></foreignObject></g></g><g transform="translate(891.82421875, 225.5)" id="flowchart-VLMO-175" class="node default default flowchart-label"><rect height="33.5" width="233.421875" y="-16.75" x="-116.7109375" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-109.2109375, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="218.421875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Vision-Language Model (VLMO)</span></div></foreignObject></g></g><g transform="translate(1383.2109375, 225.5)" id="flowchart-BLIP-177" class="node default default flowchart-label"><rect height="33.5" width="371.484375" y="-16.75" x="-185.7421875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-178.2421875, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="356.484375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Bootstrapping Language-Image Pre-training (BLIP)</span></div></foreignObject></g></g><g transform="translate(1383.2109375, 309)" id="flowchart-BEiTV2-179" class="node default default flowchart-label"><rect height="33.5" width="67.234375" y="-16.75" x="-33.6171875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-26.1171875, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="52.234375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">BEiT v2</span></div></foreignObject></g></g><g transform="translate(1383.2109375, 392.5)" id="flowchart-VL-BEiT-181" class="node default default flowchart-label"><rect height="33.5" width="237.53125" y="-16.75" x="-118.765625" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-111.265625, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="222.53125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Vision-Language BEiT (VL-BEiT)</span></div></foreignObject></g></g><g transform="translate(1652.5703125, 309)" id="flowchart-BEiTV3-183" class="node default default flowchart-label"><rect height="33.5" width="67.234375" y="-16.75" x="-33.6171875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-26.1171875, -9.25)" style="" class="label"><rect/><foreignObject height="18.5" width="52.234375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">BEiT v3</span></div></foreignObject></g></g></g></g></g></svg>
