[tool.black]
# 行长度限制
line-length = 79

# 目标Python版本
target-version = ['py38', 'py39', 'py310', 'py311']

# 包含的文件模式
include = '\.pyi?$'

# 排除的文件和目录
extend-exclude = '''
/(
  # 排除的目录
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
# 与Black兼容的import排序
profile = "black"
line_length = 79
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.pylint]
# Pylint配置
[tool.pylint.format]
max-line-length = 79

[tool.pylint.messages_control]
disable = [
    "C0114",  # missing-module-docstring
    "C0115",  # missing-class-docstring
    "C0116",  # missing-function-docstring
]

[tool.mypy]
# MyPy类型检查配置
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
